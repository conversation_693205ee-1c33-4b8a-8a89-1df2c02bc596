/* Sales Navigator Floating UI */
.sales-navigator-floating-ui {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    max-height: 80vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    border: 1px solid #e1e5e9;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.sales-nav-header {
    background: linear-gradient(135deg, #0a66c2, #004182);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px 12px 0 0;
}

.sales-nav-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.sales-nav-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.sales-nav-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.sales-nav-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.sales-nav-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.sales-nav-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sales-nav-btn.start {
    background: #28a745;
    color: white;
}

.sales-nav-btn.start:hover {
    background: #218838;
    transform: translateY(-1px);
}

.sales-nav-btn.pause {
    background: #ffc107;
    color: #212529;
}

.sales-nav-btn.pause:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.sales-nav-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.sales-nav-status {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    text-align: center;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
}

.status-dot.collecting {
    background: #28a745;
    animation: pulse-green 2s infinite;
}

.status-dot.paused {
    background: #ffc107;
}

@keyframes pulse-green {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.profiles-section {
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.profiles-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.profiles-count {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.clear-profiles {
    background: none;
    border: none;
    color: #dc3545;
    font-size: 12px;
    cursor: pointer;
    text-decoration: underline;
}

.clear-profiles:hover {
    color: #c82333;
}

.profiles-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #fafafa;
}

.profile-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.profile-item:last-child {
    border-bottom: none;
}

.profile-item:hover {
    background-color: #f1f3f4;
}

.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    margin-right: 12px;
    flex-shrink: 0;
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-name {
    font-size: 14px;
    font-weight: 600;
    color: #212529;
    margin: 0 0 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.profile-title {
    font-size: 12px;
    color: #6c757d;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.profile-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.profile-action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s;
}

.view-profile-btn {
    background: #e3f2fd;
    color: #1976d2;
}

.view-profile-btn:hover {
    background: #bbdefb;
}

.remove-profile-btn {
    background: #ffebee;
    color: #d32f2f;
}

.remove-profile-btn:hover {
    background: #ffcdd2;
}

.empty-profiles {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-size: 14px;
}

.collecting-animation {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #28a745;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sales-navigator-floating-ui {
        width: 320px;
        right: 10px;
        top: 10px;
    }
}
