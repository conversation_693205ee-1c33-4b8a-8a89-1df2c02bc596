<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Navigator UI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #0a66c2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #004182;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Sales Navigator UI Test</h1>
        <p>This page simulates a LinkedIn Sales Navigator search page for testing the floating UI.</p>
        
        <button class="test-button" onclick="testCreateUI()">Test Create UI</button>
        <button class="test-button" onclick="testShowUI()">Test Show UI</button>
        <button class="test-button" onclick="testInjectCSS()">Test Inject CSS</button>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
        
        <div id="log" class="log">
            <div>Test log will appear here...</div>
        </div>
    </div>

    <script>
        // Simulate chrome.runtime for testing
        window.chrome = {
            runtime: {
                getURL: (path) => `./${path}`
            }
        };

        // Mock the current URL to simulate Sales Navigator
        Object.defineProperty(window, 'location', {
            value: {
                href: 'https://www.linkedin.com/sales/search/people?viewAllFilters=true'
            },
            writable: true
        });

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>Log cleared...</div>';
        }

        function testCreateUI() {
            log('Testing UI creation...');
            try {
                if (window.salesNavUI) {
                    log('Sales Navigator UI already exists');
                    return;
                }
                
                // Create a simple test UI
                const ui = document.createElement('div');
                ui.className = 'sales-navigator-floating-ui';
                ui.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 350px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    padding: 20px;
                    border: 1px solid #e1e5e9;
                `;
                ui.innerHTML = `
                    <h3 style="margin: 0 0 15px 0; color: #0a66c2;">Sales Navigator Test UI</h3>
                    <p style="margin: 0 0 15px 0; font-size: 14px;">This is a test of the floating UI.</p>
                    <button onclick="this.parentElement.remove()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Close</button>
                `;
                
                document.body.appendChild(ui);
                log('Test UI created and added to DOM');
                
            } catch (error) {
                log('Error creating UI: ' + error.message);
            }
        }

        function testShowUI() {
            log('Testing UI visibility...');
            const ui = document.querySelector('.sales-navigator-floating-ui');
            if (ui) {
                ui.style.display = 'block';
                ui.style.visibility = 'visible';
                ui.style.opacity = '1';
                log('UI visibility set to visible');
            } else {
                log('No UI found to show');
            }
        }

        function testInjectCSS() {
            log('Testing CSS injection...');
            if (document.getElementById('sales-navigator-ui-styles')) {
                log('CSS already injected');
                return;
            }
            
            const style = document.createElement('style');
            style.id = 'sales-navigator-ui-styles';
            style.textContent = `
                .sales-navigator-floating-ui {
                    position: fixed !important;
                    top: 20px !important;
                    right: 20px !important;
                    z-index: 10000 !important;
                    background: white !important;
                    border-radius: 12px !important;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                }
            `;
            document.head.appendChild(style);
            log('Test CSS injected');
        }

        // Auto-run tests
        log('Page loaded - simulating Sales Navigator page');
        log('Current URL: ' + window.location.href);
        
        // Test if we can detect the page correctly
        if (window.location.href.includes('/sales/search/people')) {
            log('✓ Sales Navigator page detected correctly');
        } else {
            log('✗ Sales Navigator page detection failed');
        }
    </script>
</body>
</html>
